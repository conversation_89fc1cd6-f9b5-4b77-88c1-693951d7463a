// Global state
let cart = [];
let wishlist = [];
let currentHeroSlide = 0;

// Global DOM elements (will be initialized after DOM loads)
let mobileMenuBtn, mobileMenu, searchInput, searchSuggestions;
let cartBtn, cartDropdown, cartCount, cartItems, cartTotal;
let wishlistBtn, wishlistDropdown, wishlistCount, wishlistItems, wishlistItemCount;
let heroSlides, heroDots, heroPrev, heroNext;
let filterBtns, productCards;

// Mobile submenu toggle
window.toggleMobileSubmenu = function(category) {
    const submenu = document.getElementById(category + '-submenu');
    const arrow = document.getElementById(category + '-arrow');

    if (submenu && arrow) {
        submenu.classList.toggle('hidden');
        arrow.classList.toggle('rotate-180');
    }
};

function updateCartUI() {
    if (!cartCount || !cartTotal || !cartItems) return;

    cartCount.textContent = cart.length;
    const total = cart.reduce((sum, item) => sum + item.price, 0);
    cartTotal.textContent = `$${total.toFixed(2)}`;

    if (cart.length === 0) {
        cartItems.innerHTML = '<div class="text-center text-gray-500 py-8">Your cart is empty</div>';
    } else {
        cartItems.innerHTML = cart.map(item => `
            <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div class="w-12 h-12 bg-secondary rounded-lg"></div>
                <div class="flex-1">
                    <h4 class="font-medium text-sm">${item.name}</h4>
                    <p class="text-primary font-bold text-sm">$${item.price}</p>
                </div>
                <button onclick="removeFromCart(${item.id})" class="text-red-500 hover:text-red-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `).join('');
    }
}

function updateWishlistUI() {
    if (!wishlistCount || !wishlistItemCount || !wishlistItems) return;

    wishlistCount.textContent = wishlist.length;
    wishlistItemCount.textContent = wishlist.length;

    if (wishlist.length === 0) {
        wishlistItems.innerHTML = '<div class="text-center text-gray-500 py-8">Your wishlist is empty</div>';
    } else {
        wishlistItems.innerHTML = wishlist.map(item => `
            <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-heart text-red-500"></i>
                </div>
                <div class="flex-1">
                    <h4 class="font-medium text-sm">${item.name}</h4>
                    <p class="text-gray-500 text-xs">Added to wishlist</p>
                </div>
                <button onclick="removeFromWishlist(${item.id})" class="text-red-500 hover:text-red-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `).join('');
    }
}

window.addToCart = function(productId, productName, productPrice) {
    const item = { id: productId, name: productName, price: parseFloat(productPrice) };
    cart.push(item);
    updateCartUI();

    // Show success message
    showNotification('Added to cart!', 'success');
};

window.removeFromCart = function(productId) {
    cart = cart.filter(item => item.id !== productId);
    updateCartUI();
};

window.toggleWishlist = function(productId, productName) {
    const existingIndex = wishlist.findIndex(item => item.id === productId);

    if (existingIndex > -1) {
        wishlist.splice(existingIndex, 1);
        showNotification('Removed from wishlist', 'info');
    } else {
        wishlist.push({ id: productId, name: productName });
        showNotification('Added to wishlist!', 'success');
    }

    updateWishlistUI();
};

window.removeFromWishlist = function(productId) {
    wishlist = wishlist.filter(item => item.id !== productId);
    updateWishlistUI();
};

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-24 right-4 z-50 px-6 py-3 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300 ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Hero slider functions
function updateHeroSlider() {
    if (!heroSlides || !heroDots) return;

    heroSlides.forEach((slide, index) => {
        slide.style.opacity = index === currentHeroSlide ? '1' : '0';
    });

    heroDots.forEach((dot, index) => {
        if (index === currentHeroSlide) {
            dot.classList.add('bg-primary');
            dot.classList.remove('bg-gray-300');
        } else {
            dot.classList.remove('bg-primary');
            dot.classList.add('bg-gray-300');
        }
    });
}

function initHeroSlider() {
    if (!heroSlides || !heroDots || !heroPrev || !heroNext) return;

    heroPrev.addEventListener('click', () => {
        currentHeroSlide = currentHeroSlide === 0 ? heroSlides.length - 1 : currentHeroSlide - 1;
        updateHeroSlider();
    });

    heroNext.addEventListener('click', () => {
        currentHeroSlide = currentHeroSlide === heroSlides.length - 1 ? 0 : currentHeroSlide + 1;
        updateHeroSlider();
    });

    heroDots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            currentHeroSlide = index;
            updateHeroSlider();
        });
    });

    // Auto-advance hero slider
    setInterval(() => {
        if (heroSlides && heroSlides.length > 0) {
            currentHeroSlide = currentHeroSlide === heroSlides.length - 1 ? 0 : currentHeroSlide + 1;
            updateHeroSlider();
        }
    }, 7000);
}

// Product filtering functions
function initProductFiltering() {
    if (!filterBtns || !productCards) return;

    filterBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const filter = btn.dataset.filter;

            // Update active button with enhanced styling
            filterBtns.forEach(b => {
                b.classList.remove('active');
                // Reset to default glassmorphism style
                b.style.background = 'rgba(255, 255, 255, 0.9)';
                b.style.color = '#6B7280';
                b.style.transform = 'translateY(0)';
                b.style.boxShadow = '';
            });

            // Apply active styling
            btn.classList.add('active');

            // Filter products with animation
            let visibleCount = 0;
            productCards.forEach((card, index) => {
                const category = card.dataset.category;
                if (filter === 'all' || category === filter) {
                    card.style.display = 'block';
                    // Stagger animation for visible cards
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, index * 50);
                    visibleCount++;
                } else {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    setTimeout(() => {
                        card.style.display = 'none';
                    }, 300);
                }
            });

            const productCountEl = document.getElementById('product-count');
            if (productCountEl) {
                productCountEl.textContent = visibleCount;
            }
        });
    });
}

// Product interactions
function initProductInteractions() {
    const wishlistBtns = document.querySelectorAll('.wishlist-btn');
    const addToCartBtns = document.querySelectorAll('.add-to-cart-btn');

    wishlistBtns.forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.stopPropagation();
            const productCard = btn.closest('.product-card');
            if (!productCard) return;

            const productId = Math.random().toString(36).substr(2, 9);
            const productNameEl = productCard.querySelector('h4');
            const productName = productNameEl ? productNameEl.textContent : 'Unknown Product';

            toggleWishlist(productId, productName);

            const icon = btn.querySelector('i');
            if (icon) {
                icon.classList.toggle('fas');
                icon.classList.toggle('far');
                icon.classList.toggle('text-red-500');
            }
        });
    });

    addToCartBtns.forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.stopPropagation();
            const productCard = btn.closest('.product-card');
            if (!productCard) return;

            const productId = Math.random().toString(36).substr(2, 9);
            const productNameEl = productCard.querySelector('h4');
            const productName = productNameEl ? productNameEl.textContent : 'Unknown Product';
            const productPrice = productCard.dataset.price || '0';

            addToCart(productId, productName, productPrice);
        });
    });
}

// Smooth scrolling for navigation links
function initSmoothScrolling() {
    const anchors = document.querySelectorAll('a[href^="#"]');
    anchors.forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Navbar scroll effect
function initNavbarScrollEffect() {
    window.addEventListener('scroll', () => {
        const navbar = document.getElementById('navbar');
        if (navbar) {
            if (window.scrollY > 100) {
                navbar.classList.add('bg-white/98', 'shadow-2xl');
                navbar.classList.remove('bg-white/90');
            } else {
                navbar.classList.remove('bg-white/98', 'shadow-2xl');
                navbar.classList.add('bg-white/90');
            }
        }
    });
}

// Authentication Functions
function updateAuthUI(user) {
    // Update desktop login/signup buttons to show user info
    const authButtons = document.querySelector('.auth-buttons');
    if (authButtons && user) {
        // Create role-based navigation buttons
        let roleBasedButtons = '';
        if (user.role === 'admin' || user.role === 'super_admin') {
            roleBasedButtons = `
                <a href="/admin.html" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors duration-300 text-sm font-medium">
                    <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                </a>
            `;
        } else {
            roleBasedButtons = `
                <button onclick="showUserProfile()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-300 text-sm font-medium">
                    <i class="fas fa-user mr-2"></i>Profile
                </button>
            `;
        }

        authButtons.innerHTML = `
            <div class="flex items-center space-x-4">
                ${roleBasedButtons}
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-medium">
                        ${user.first_name.charAt(0).toUpperCase()}
                    </div>
                    <span class="text-gray-700 font-medium">${user.first_name}</span>
                    ${user.role === 'admin' || user.role === 'super_admin' ?
                        '<span class="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">Admin</span>' : ''}
                </div>
                <button onclick="logout()" class="text-gray-600 hover:text-gray-800 transition-colors duration-300 p-2 rounded-lg hover:bg-gray-100" title="Logout">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        `;
    }

    // Update mobile auth UI
    const mobileAuthContainer = document.querySelector('.mobile-auth-container');
    if (mobileAuthContainer && user) {
        // Create role-based mobile navigation buttons
        let mobileRoleBasedButtons = '';
        if (user.role === 'admin' || user.role === 'super_admin') {
            mobileRoleBasedButtons = `
                <a href="/admin.html" class="w-full bg-purple-600 text-white px-3 py-3 rounded-lg font-medium hover:bg-purple-700 transition duration-300 text-center block mb-3">
                    <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                </a>
            `;
        } else {
            mobileRoleBasedButtons = `
                <button onclick="showUserProfile()" class="w-full bg-blue-600 text-white px-3 py-3 rounded-lg font-medium hover:bg-blue-700 transition duration-300 mb-3">
                    <i class="fas fa-user mr-2"></i>Profile
                </button>
            `;
        }

        mobileAuthContainer.innerHTML = `
            <div class="border-t border-gray-200 pt-4 mt-4 space-y-3">
                ${mobileRoleBasedButtons}
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-primary text-white rounded-full flex items-center justify-center text-sm font-medium">
                            ${user.first_name.charAt(0).toUpperCase()}
                        </div>
                        <div>
                            <p class="font-medium text-gray-900">${user.first_name} ${user.last_name || ''}</p>
                            <p class="text-sm text-gray-600">${user.email}</p>
                            ${user.role === 'admin' || user.role === 'super_admin' ?
                                '<span class="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">Admin</span>' : ''}
                        </div>
                    </div>
                    <button onclick="logout()" class="text-red-600 hover:text-red-800 transition-colors duration-300 p-2 rounded-lg hover:bg-red-50" title="Logout">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            </div>
        `;
    }
}

// Show user profile function
window.showUserProfile = function() {
    // For now, show a simple modal with user info
    // In a full implementation, this would navigate to a profile page
    const user = window.apiClient.getCurrentUser();
    if (!user) return;

    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-800">User Profile</h3>
                <button onclick="this.closest('.fixed').remove()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="space-y-4">
                <div class="flex items-center space-x-4">
                    <div class="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center text-xl font-medium">
                        ${user.first_name.charAt(0).toUpperCase()}
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-800">${user.first_name} ${user.last_name || ''}</h4>
                        <p class="text-sm text-gray-600">${user.email}</p>
                        <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full mt-1">
                            ${user.role.charAt(0).toUpperCase() + user.role.slice(1)} User
                        </span>
                    </div>
                </div>
                <div class="border-t pt-4">
                    <p class="text-sm text-gray-600 mb-4">Profile features coming soon! You'll be able to:</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Update personal information</li>
                        <li>• View order history</li>
                        <li>• Manage addresses</li>
                        <li>• Change password</li>
                    </ul>
                </div>
                <div class="flex space-x-3 pt-4">
                    <button onclick="this.closest('.fixed').remove()" class="flex-1 bg-gray-200 text-gray-800 py-2 rounded-lg hover:bg-gray-300 transition-colors duration-300">
                        Close
                    </button>
                    <button onclick="logout()" class="flex-1 bg-red-500 text-white py-2 rounded-lg hover:bg-red-600 transition-colors duration-300">
                        Logout
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === this) {
            this.remove();
        }
    });
};

async function logout() {
    try {
        // Show loading notification
        showNotification('Logging out...', 'info');

        // Call server logout endpoint (optional - for session cleanup)
        try {
            await window.apiClient.request('/api/auth/logout', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${window.apiClient.getToken()}`
                }
            });
        } catch (error) {
            console.warn('Server logout failed, continuing with client logout:', error);
        }

        // Clear client-side data
        window.apiClient.removeToken();
        window.apiClient.removeCurrentUser();

        // Clear any other stored data
        localStorage.removeItem('cartItems');
        localStorage.removeItem('wishlistItems');
        sessionStorage.clear();

        // Reset auth UI for desktop
        const authButtons = document.querySelector('.auth-buttons');
        if (authButtons) {
            authButtons.innerHTML = `
                <button onclick="openLoginModal()" class="text-gray-700 hover:text-primary transition-colors duration-300 font-medium">
                    Login
                </button>
                <button onclick="openSignupModal()" class="bg-primary text-white px-6 py-2 rounded-full hover:bg-accent transition-all duration-300 font-medium">
                    Sign Up
                </button>
            `;
        }

        // Reset mobile auth UI
        const mobileAuthContainer = document.querySelector('.mobile-auth-container');
        if (mobileAuthContainer) {
            mobileAuthContainer.innerHTML = `
                <button onclick="openLoginModal()" class="w-full px-3 py-3 text-gray-700 hover:text-primary hover:bg-purple-light rounded-lg transition duration-300 font-medium text-left">
                    Login
                </button>
                <button onclick="openSignupModal()" class="w-full bg-primary text-white px-3 py-3 rounded-lg font-medium hover:bg-accent transition duration-300">
                    Sign Up
                </button>
            `;
        }

        // Update cart and wishlist UI
        updateCartUI();
        updateWishlistUI();

        showNotification('Logged out successfully', 'success');

        // Redirect to homepage if on admin page
        if (window.location.pathname.includes('admin')) {
            setTimeout(() => {
                window.location.href = '/';
            }, 1500);
        }

    } catch (error) {
        console.error('Logout error:', error);
        showNotification('Logout failed. Please try again.', 'error');
    }
}

// Check if user is already logged in on page load
function checkAuthStatus() {
    const token = window.apiClient.getToken();
    const user = window.apiClient.getCurrentUser();

    if (token && user) {
        updateAuthUI(user);
    }
}

// Authentication Modal Functions
window.openLoginModal = function() {
    const modal = document.getElementById('login-modal');
    const content = document.getElementById('login-modal-content');
    modal.classList.remove('hidden');
    setTimeout(() => {
        content.classList.remove('scale-95');
        content.classList.add('scale-100');
    }, 10);
    document.body.style.overflow = 'hidden';
};

window.openSignupModal = function() {
    const modal = document.getElementById('signup-modal');
    const content = document.getElementById('signup-modal-content');
    modal.classList.remove('hidden');
    setTimeout(() => {
        content.classList.remove('scale-95');
        content.classList.add('scale-100');
    }, 10);
    document.body.style.overflow = 'hidden';
};

window.closeAuthModal = function() {
    const loginModal = document.getElementById('login-modal');
    const signupModal = document.getElementById('signup-modal');
    const loginContent = document.getElementById('login-modal-content');
    const signupContent = document.getElementById('signup-modal-content');

    loginContent.classList.add('scale-95');
    signupContent.classList.add('scale-95');

    setTimeout(() => {
        loginModal.classList.add('hidden');
        signupModal.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }, 300);
};

window.switchToSignup = function() {
    const loginModal = document.getElementById('login-modal');
    const signupModal = document.getElementById('signup-modal');
    const loginContent = document.getElementById('login-modal-content');
    const signupContent = document.getElementById('signup-modal-content');

    loginContent.classList.add('scale-95');
    setTimeout(() => {
        loginModal.classList.add('hidden');
        signupModal.classList.remove('hidden');
        setTimeout(() => {
            signupContent.classList.remove('scale-95');
            signupContent.classList.add('scale-100');
        }, 10);
    }, 150);
};

window.switchToLogin = function() {
    const loginModal = document.getElementById('login-modal');
    const signupModal = document.getElementById('signup-modal');
    const loginContent = document.getElementById('login-modal-content');
    const signupContent = document.getElementById('signup-modal-content');

    signupContent.classList.add('scale-95');
    setTimeout(() => {
        signupModal.classList.add('hidden');
        loginModal.classList.remove('hidden');
        setTimeout(() => {
            loginContent.classList.remove('scale-95');
            loginContent.classList.add('scale-100');
        }, 10);
    }, 150);
};

// Password visibility toggle
window.togglePasswordVisibility = function(inputId) {
    const input = document.getElementById(inputId);
    const icon = document.getElementById(inputId + '-icon');

    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
};

// Form validation
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

function checkPasswordStrength(password) {
    let strength = 0;
    const checks = [
        password.length >= 8,
        /[a-z]/.test(password),
        /[A-Z]/.test(password),
        /[0-9]/.test(password),
        /[^A-Za-z0-9]/.test(password)
    ];

    strength = checks.filter(Boolean).length;
    return strength;
}

// Load featured products from API
async function loadFeaturedProducts() {
    const productsGrid = document.getElementById('products-grid');
    if (!productsGrid) return;

    try {
        showLoading(productsGrid);

        // Fetch products from API
        const response = await window.apiClient.getProducts({
            limit: 8,
            featured: true
        });

        if (response.success && response.data && response.data.length > 0) {
            productsGrid.innerHTML = '';

            response.data.forEach((product, index) => {
                const productCard = createProductCardFromAPI(product);
                productCard.style.opacity = '0';
                productCard.style.transform = 'translateY(20px)';
                productsGrid.appendChild(productCard);

                // Stagger animation
                setTimeout(() => {
                    productCard.style.opacity = '1';
                    productCard.style.transform = 'translateY(0)';
                    productCard.style.transition = 'all 0.5s ease-out';
                }, index * 100);
            });

            // Update product count
            const productCount = document.getElementById('product-count');
            if (productCount) {
                productCount.textContent = response.data.length;
            }
        } else {
            // Fallback to static products if API fails
            console.warn('No products returned from API, using fallback');
            loadStaticProducts();
        }
    } catch (error) {
        console.error('Failed to load products from API:', error);
        // Fallback to static products
        loadStaticProducts();
    }
}

// Create product card from API data
function createProductCardFromAPI(product) {
    const card = document.createElement('div');
    card.className = 'product-card group cursor-pointer';
    card.setAttribute('data-category', product.category_slug || 'general');
    card.setAttribute('data-price', product.sale_price || product.price);
    card.setAttribute('data-rating', product.avg_rating || 4.5);

    const price = product.sale_price || product.price;
    const originalPrice = product.sale_price ? product.price : null;
    const badge = product.badge || (product.is_featured ? 'Featured' : '');
    const rating = product.avg_rating || 4.5;
    const reviewCount = product.review_count || 0;

    const badgeHtml = badge ? `<span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full font-medium">${badge}</span>` : '';
    const priceHtml = originalPrice ?
        `<span class="text-2xl font-bold text-primary">$${price}</span>
         <span class="text-sm text-gray-500 line-through ml-2">$${originalPrice}</span>` :
        `<span class="text-2xl font-bold text-primary">$${price}</span>`;

    card.innerHTML = `
        <div class="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden">
            <div class="relative h-72 bg-gradient-to-br from-secondary to-purple-light overflow-hidden">
                ${badgeHtml ? `<div class="absolute top-4 left-4 z-10">${badgeHtml}</div>` : ''}

                <!-- Product Image -->
                ${product.primary_image ?
                    `<img src="${product.primary_image}" alt="${product.name}" class="absolute inset-0 w-full h-full object-cover">` :
                    `<div class="absolute inset-0 flex items-center justify-center">
                        <i class="fas fa-image text-gray-400 text-4xl"></i>
                    </div>`
                }

                <div class="absolute top-4 right-4 z-10 flex flex-col space-y-2 opacity-0 group-hover:opacity-100 transition-all duration-300">
                    <button class="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors duration-300 wishlist-btn" onclick="toggleWishlist(${product.id}, '${product.name}')">
                        <i class="far fa-heart text-gray-600 hover:text-red-500 text-sm"></i>
                    </button>
                    <button class="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors duration-300 quick-view-btn" onclick="viewProduct(${product.id})">
                        <i class="fas fa-eye text-gray-600 hover:text-primary text-sm"></i>
                    </button>
                </div>

                <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                <div class="absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all duration-300">
                    <button class="w-full bg-primary text-white py-2 rounded-lg font-medium hover:bg-accent transition-colors duration-300 add-to-cart-btn" onclick="addToCart(${product.id}, '${product.name}', ${price})">
                        Add to Cart
                    </button>
                </div>
            </div>

            <div class="p-6">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-xs text-gray-500 uppercase tracking-wide">${product.category_name || 'Fashion'}</span>
                    <div class="flex items-center space-x-1">
                        <div class="flex text-yellow-400">
                            ${generateStars(rating)}
                        </div>
                        <span class="text-xs text-gray-500">(${rating})</span>
                    </div>
                </div>
                <h4 class="font-semibold text-gray-800 mb-2 group-hover:text-primary transition-colors duration-300">${product.name}</h4>
                <p class="text-sm text-gray-600 mb-3 line-clamp-2">${product.short_description || 'Premium quality fashion item'}</p>
                <div class="flex items-center justify-between">
                    ${priceHtml}
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500">Free Shipping</span>
                    </div>
                </div>
            </div>
        </div>
    `;

    return card;
}

// Fallback function to load static products
function loadStaticProducts() {
    const productsGrid = document.getElementById('products-grid');
    if (!productsGrid) return;

    // Keep existing static product loading logic as fallback
    console.log('Loading static products as fallback');
}

// View product function
window.viewProduct = function(productId) {
    // Navigate to product detail page or open modal
    window.location.href = `products.html?id=${productId}`;
};

// Generate star rating HTML
function generateStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    let starsHtml = '';

    for (let i = 0; i < fullStars; i++) {
        starsHtml += '<i class="fas fa-star text-xs"></i>';
    }

    if (hasHalfStar) {
        starsHtml += '<i class="fas fa-star-half-alt text-xs"></i>';
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
        starsHtml += '<i class="far fa-star text-xs"></i>';
    }

    return starsHtml;
}

// Initialization functions
function initMobileMenu() {
    if (mobileMenuBtn && mobileMenu) {
        mobileMenuBtn.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });
    }
}

function initSearchFunctionality() {
    if (searchInput && searchSuggestions) {
        searchInput.addEventListener('focus', () => {
            searchSuggestions.classList.remove('hidden');
        });

        searchInput.addEventListener('blur', () => {
            setTimeout(() => {
                searchSuggestions.classList.add('hidden');
            }, 200);
        });
    }
}

function initCartFunctionality() {
    if (cartBtn && cartDropdown) {
        cartBtn.addEventListener('click', () => {
            cartDropdown.classList.toggle('opacity-0');
            cartDropdown.classList.toggle('invisible');
        });
    }
}

function initWishlistFunctionality() {
    if (wishlistBtn && wishlistDropdown) {
        wishlistBtn.addEventListener('click', () => {
            wishlistDropdown.classList.toggle('opacity-0');
            wishlistDropdown.classList.toggle('invisible');
        });
    }
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize DOM elements
    mobileMenuBtn = document.getElementById('mobile-menu-btn');
    mobileMenu = document.getElementById('mobile-menu');
    searchInput = document.getElementById('search-input');
    searchSuggestions = document.getElementById('search-suggestions');
    cartBtn = document.getElementById('cart-btn');
    cartDropdown = document.getElementById('cart-dropdown');
    cartCount = document.getElementById('cart-count');
    cartItems = document.getElementById('cart-items');
    cartTotal = document.getElementById('cart-total');
    wishlistBtn = document.getElementById('wishlist-btn');
    wishlistDropdown = document.getElementById('wishlist-dropdown');
    wishlistCount = document.getElementById('wishlist-count');
    wishlistItems = document.getElementById('wishlist-items');
    wishlistItemCount = document.getElementById('wishlist-item-count');
    heroSlides = document.querySelectorAll('.hero-slide');
    heroDots = document.querySelectorAll('.hero-dot');
    heroPrev = document.getElementById('hero-prev');
    heroNext = document.getElementById('hero-next');
    filterBtns = document.querySelectorAll('.product-filter-btn');
    productCards = document.querySelectorAll('.product-card');

    // Initialize event listeners
    initMobileMenu();
    initSearchFunctionality();
    initCartFunctionality();
    initWishlistFunctionality();
    initHeroSlider();
    initProductFiltering();
    initProductInteractions();
    initSmoothScrolling();
    initNavbarScrollEffect();

    // Initialize UI
    updateCartUI();
    updateWishlistUI();
    updateHeroSlider();

    // Load featured products from API
    loadFeaturedProducts();

    // Check authentication status
    checkAuthStatus();

    // Real-time validation for signup form
    document.getElementById('signup-email')?.addEventListener('input', function() {
        const email = this.value;
        const validation = document.getElementById('email-validation');

        if (email && !validateEmail(email)) {
            validation.classList.remove('hidden');
            this.classList.add('border-red-500');
        } else {
            validation.classList.add('hidden');
            this.classList.remove('border-red-500');
        }
    });

    document.getElementById('signup-password')?.addEventListener('input', function() {
        const password = this.value;
        const strengthIndicator = document.getElementById('password-strength');
        const strengthText = document.getElementById('strength-text');

        if (password) {
            strengthIndicator.classList.remove('hidden');
            const strength = checkPasswordStrength(password);

            // Reset all strength indicators
            for (let i = 1; i <= 4; i++) {
                const indicator = document.getElementById(`strength-${i}`);
                indicator.classList.remove('bg-red-500', 'bg-yellow-500', 'bg-green-500');
                indicator.classList.add('bg-gray-200');
            }

            // Update strength indicators
            const colors = ['bg-red-500', 'bg-red-500', 'bg-yellow-500', 'bg-green-500'];
            const texts = ['Very Weak', 'Weak', 'Fair', 'Strong'];

            for (let i = 1; i <= Math.min(strength, 4); i++) {
                const indicator = document.getElementById(`strength-${i}`);
                indicator.classList.remove('bg-gray-200');
                indicator.classList.add(colors[strength - 1]);
            }

            strengthText.textContent = texts[Math.min(strength - 1, 3)] || 'Very Weak';
            strengthText.className = `text-sm ${strength >= 3 ? 'text-green-600' : strength >= 2 ? 'text-yellow-600' : 'text-red-600'}`;
        } else {
            strengthIndicator.classList.add('hidden');
        }
    });

    document.getElementById('signup-confirm-password')?.addEventListener('input', function() {
        const password = document.getElementById('signup-password').value;
        const confirmPassword = this.value;
        const matchIndicator = document.getElementById('password-match');

        if (confirmPassword && password !== confirmPassword) {
            matchIndicator.classList.remove('hidden');
            this.classList.add('border-red-500');
        } else {
            matchIndicator.classList.add('hidden');
            this.classList.remove('border-red-500');
        }
    });

    // Form submissions
    document.getElementById('login-form')?.addEventListener('submit', async function(e) {
        e.preventDefault();
        const email = document.getElementById('login-email').value;
        const password = document.getElementById('login-password').value;

        // Validate inputs
        if (!email || !password) {
            showNotification('Please fill in all fields', 'error');
            return;
        }

        try {
            showNotification('Logging in...', 'info');

            const response = await window.apiClient.login({ email, password });

            if (response.success && response.data) {
                // Store token and user data
                window.apiClient.setToken(response.data.token);
                window.apiClient.setCurrentUser(response.data.user);

                showNotification('Login successful!', 'success');
                closeAuthModal();
                updateAuthUI(response.data.user);

                // Redirect to admin if user is admin
                if (response.data.user.role === 'admin' || response.data.user.role === 'super_admin') {
                    setTimeout(() => {
                        if (confirm('You have admin privileges. Would you like to go to the admin dashboard?')) {
                            window.location.href = '/admin.html';
                        }
                    }, 1000);
                }
            } else {
                showNotification(response.message || 'Login failed', 'error');
            }
        } catch (error) {
            console.error('Login error:', error);
            showNotification('Login failed. Please check your credentials.', 'error');
        }
    });

    document.getElementById('signup-form')?.addEventListener('submit', async function(e) {
        e.preventDefault();
        const name = document.getElementById('signup-name').value;
        const email = document.getElementById('signup-email').value;
        const password = document.getElementById('signup-password').value;
        const confirmPassword = document.getElementById('signup-confirm-password').value;
        const termsAccepted = document.getElementById('terms-checkbox').checked;

        // Validation
        if (!name || !email || !password || !confirmPassword) {
            showNotification('Please fill in all fields', 'error');
            return;
        }

        if (!validateEmail(email)) {
            showNotification('Please enter a valid email address', 'error');
            return;
        }

        if (password !== confirmPassword) {
            showNotification('Passwords do not match', 'error');
            return;
        }

        if (checkPasswordStrength(password) < 2) {
            showNotification('Please choose a stronger password', 'error');
            return;
        }

        if (!termsAccepted) {
            showNotification('Please accept the terms of service', 'error');
            return;
        }

        try {
            showNotification('Creating account...', 'info');

            // Split name into first and last name
            const nameParts = name.trim().split(' ');
            const first_name = nameParts[0];
            const last_name = nameParts.slice(1).join(' ') || '';

            const response = await window.apiClient.register({
                email,
                password,
                first_name,
                last_name
            });

            if (response.success && response.data) {
                // Store token and user data
                window.apiClient.setToken(response.data.token);
                window.apiClient.setCurrentUser(response.data.user);

                showNotification('Account created successfully!', 'success');
                closeAuthModal();
                updateAuthUI(response.data.user);
            } else {
                showNotification(response.message || 'Registration failed', 'error');
            }
        } catch (error) {
            console.error('Registration error:', error);
            showNotification('Registration failed. Please try again.', 'error');
        }
    });

    // Close modals when clicking outside
    document.getElementById('login-modal')?.addEventListener('click', function(e) {
        if (e.target === this) {
            closeAuthModal();
        }
    });

    document.getElementById('signup-modal')?.addEventListener('click', function(e) {
        if (e.target === this) {
            closeAuthModal();
        }
    });

    // Close modals with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeAuthModal();
        }
    });

    // Newsletter subscription
    const newsletterSection = document.querySelector('section input[type="email"]')?.closest('section');
    const emailInput = newsletterSection?.querySelector('input[type="email"]');
    const subscribeBtn = newsletterSection?.querySelector('button');

    subscribeBtn?.addEventListener('click', (e) => {
        e.preventDefault();
        const email = emailInput.value.trim();

        if (email && email.includes('@')) {
            subscribeBtn.textContent = 'Subscribed!';
            subscribeBtn.classList.add('bg-green-500');
            emailInput.value = '';

            setTimeout(() => {
                subscribeBtn.textContent = 'Subscribe';
                subscribeBtn.classList.remove('bg-green-500');
            }, 3000);
        } else {
            emailInput.classList.add('border-red-500');
            setTimeout(() => {
                emailInput.classList.remove('border-red-500');
            }, 3000);
        }
    });

    // Scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in-up');
            }
        });
    }, observerOptions);

    document.querySelectorAll('section').forEach(section => {
        observer.observe(section);
    });





    // Simulate loading
    setTimeout(() => {
        document.body.classList.add('loaded');
    }, 500);
});




