# Luxe Fashion - Full-Stack E-commerce Application

## Overview
Luxe Fashion is a modern, full-stack e-commerce application for a premium clothing and accessories brand. The application features a consolidated Express.js server that serves both the backend API and frontend, providing a seamless shopping experience with intuitive navigation, responsive design, and comprehensive e-commerce functionality.

## Technologies Used

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **MySQL** - Database with connection pooling
- **JWT** - Authentication and authorization
- **bcryptjs** - Password hashing
- **Helmet** - Security middleware
- **CORS** - Cross-origin resource sharing
- **Express Rate Limit** - API rate limiting

### Frontend
- **HTML5** - Structure and content
- **CSS3** - Custom styling
- **JavaScript (ES6+)** - Interactive functionality
- **Tailwind CSS** - Utility-first styling and responsive design
- **Font Awesome** - Icons
- **Google Fonts** - Typography (Inter font family)

## Features

### Frontend Features
- **Modern UI/UX Design** - Clean, elegant interface with animations and transitions
- **Responsive Layout** - Fully responsive design that works on all device sizes
- **Product Catalog** - Organized display of fashion products with filtering and sorting
- **Interactive Elements** - Animated components, hover effects, and smooth transitions
- **SPA Routing** - Single-page application navigation
- **Real-time Cart & Wishlist** - Dynamic shopping cart and wishlist management

### Backend Features
- **RESTful API** - Comprehensive API for all e-commerce operations
- **User Authentication** - JWT-based login and registration system
- **Admin Dashboard** - Complete admin panel for managing products, orders, and users
- **Shopping Cart** - Persistent cart functionality with user sessions
- **Order Management** - Complete order processing and tracking
- **Product Management** - CRUD operations for products with categories
- **Security Features** - Rate limiting, input validation, and SQL injection prevention
- **Newsletter System** - Email subscription management

## Installation and Setup

### Prerequisites
- **Node.js 16+** - Runtime environment
- **MySQL 8.0+** - Database server
- **npm** - Package manager

### Quick Start
1. **Clone the repository:**
   ```bash
   git clone https://github.com/yourusername/luxe-fashion.git
   cd luxe-fashion
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Configure environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your database credentials and settings
   ```

4. **Set up the database:**
   ```bash
   npm run db:setup
   ```

5. **Start the application:**
   ```bash
   # Production mode
   npm start

   # Development mode (with auto-reload)
   npm run dev
   ```

6. **Access the application:**
   - **Website**: http://localhost:3001
   - **API**: http://localhost:3001/api/
   - **Admin**: http://localhost:3001/admin.html

## Project Structure
```
luxe-fashion-app/
├── server.js                    # Main server file (consolidated)
├── package.json                 # Dependencies and scripts
├── .env                         # Environment configuration
├── api-config.js                # Frontend API configuration
├── index.html                   # Homepage
├── products.html                # Products page
├── admin.html                   # Admin dashboard
├── checkout.html                # Checkout page
├── script.js                    # Main frontend JavaScript
├── products.js                  # Products page JavaScript
├── admin.js                     # Admin dashboard JavaScript
├── styles.css                   # Main stylesheet
├── config/                      # Server configuration
│   └── database.js              # Database connection
├── controllers/                 # API controllers
│   ├── authController.js        # Authentication logic
│   ├── productController.js     # Product management
│   ├── cartController.js        # Shopping cart
│   ├── orderController.js       # Order processing
│   └── adminController.js       # Admin operations
├── middleware/                  # Express middleware
│   ├── auth.js                  # Authentication middleware
│   ├── security.js              # Security middleware
│   └── validation.js            # Input validation
├── routes/                      # API routes
│   ├── auth.js                  # Authentication routes
│   ├── products.js              # Product routes
│   ├── cart.js                  # Cart routes
│   └── admin.js                 # Admin routes
├── database/                    # Database scripts
│   ├── schema.sql               # Database schema
│   ├── seed.js                  # Sample data
│   └── migrate.js               # Migration scripts
├── utils/                       # Utility functions
└── uploads/                     # File uploads directory
```

## Consolidated Architecture Benefits

### Single Server Deployment
- **Simplified Deployment** - One server to deploy and manage
- **Reduced Complexity** - No need to coordinate multiple servers
- **Cost Effective** - Lower hosting and infrastructure costs
- **Easier Development** - Single codebase for both frontend and backend

### Performance Advantages
- **Reduced Latency** - No cross-origin requests for API calls
- **Better Caching** - Unified caching strategy for static and dynamic content
- **Simplified CORS** - No complex CORS configuration needed
- **Single SSL Certificate** - One certificate covers both frontend and API

### Development Benefits
- **Unified Logging** - All requests logged in one place
- **Shared Configuration** - Single environment configuration
- **Easier Testing** - Test both frontend and backend together
- **Simplified CI/CD** - Single build and deployment pipeline

## API Documentation

For detailed API documentation, see the [CONSOLIDATED_APP_GUIDE.md](./CONSOLIDATED_APP_GUIDE.md) file.

### Key API Endpoints
- `GET /health` - Server health check
- `POST /api/auth/login` - User authentication
- `GET /api/products` - Product catalog
- `POST /api/cart/add` - Add items to cart
- `GET /api/admin/dashboard` - Admin analytics

## Future Development Plans

### Planned Enhancements
- **Payment Integration** - Stripe/PayPal checkout processing
- **Email Notifications** - Order confirmations and shipping updates
- **Inventory Management** - Real-time stock tracking
- **Advanced Search** - Elasticsearch integration for better search
- **Product Reviews** - Customer feedback and ratings system
- **Recommendation Engine** - AI-based product suggestions
- **Mobile App** - React Native mobile application
- **Multi-language Support** - Internationalization (i18n)

### Performance Optimizations
- **Image Optimization** - WebP format and lazy loading
- **Caching Strategies** - Redis for session and data caching
- **CDN Integration** - CloudFront for static asset delivery
- **Database Optimization** - Query optimization and indexing
- **Load Balancing** - Horizontal scaling with multiple instances

## Deployment

### Production Deployment
1. **Environment Setup:**
   ```bash
   NODE_ENV=production
   PORT=3001
   # Configure production database and JWT secrets
   ```

2. **Using PM2 (Recommended):**
   ```bash
   npm install -g pm2
   pm2 start server.js --name "luxe-fashion"
   pm2 startup
   pm2 save
   ```

3. **Using Docker:**
   ```dockerfile
   FROM node:16-alpine
   WORKDIR /app
   COPY package*.json ./
   RUN npm ci --only=production
   COPY . .
   EXPOSE 3001
   CMD ["npm", "start"]
   ```

### Security Considerations
- Use HTTPS in production
- Set secure JWT secrets
- Configure proper CORS origins
- Enable rate limiting
- Regular security updates

## Contributing
Contributions are welcome! Please feel free to submit a Pull Request.

## License
This project is licensed under the MIT License - see the LICENSE file for details.