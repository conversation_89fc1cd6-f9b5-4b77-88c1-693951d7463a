# Luxe Fashion - Consolidated Application Guide

## Overview

The Luxe Fashion application has been successfully consolidated into a single Express.js server that serves both the backend API and frontend static files. This simplifies deployment and development while maintaining all existing functionality.

## Architecture

### Single Server Setup
- **Server**: Express.js running on port 3001 (configurable via `.env`)
- **Frontend**: Static HTML, CSS, and JavaScript files served from the same server
- **Backend**: RESTful API with `/api/` prefix
- **Database**: MySQL with connection pooling

### Key Features
- ✅ Unified server serving both frontend and API
- ✅ SPA (Single Page Application) routing support
- ✅ CORS configured for same-origin requests
- ✅ Security middleware (Helmet, rate limiting)
- ✅ Static file serving with proper MIME types
- ✅ API error handling separate from frontend routing

## Quick Start

### Prerequisites
- Node.js 16+ installed
- MySQL database running
- Environment variables configured in `.env`

### Installation & Setup
```bash
# Install dependencies
npm install

# Set up database (if not already done)
npm run db:setup

# Start the consolidated application
npm start
```

### Development Mode
```bash
# Start with auto-reload
npm run dev
```

## Application URLs

Once started, access the application at:

- **Homepage**: http://localhost:3001/
- **Products**: http://localhost:3001/products.html
- **Admin Dashboard**: http://localhost:3001/admin.html
- **Checkout**: http://localhost:3001/checkout.html
- **API Health**: http://localhost:3001/health
- **Products API**: http://localhost:3001/api/products

## API Endpoints

All API endpoints are prefixed with `/api/`:

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `GET /api/auth/me` - Get current user

### Products
- `GET /api/products` - Get all products
- `GET /api/products/:id` - Get product by ID
- `GET /api/products/category/:category` - Get products by category
- `GET /api/products/search` - Search products

### Cart & Wishlist
- `GET /api/cart` - Get user's cart
- `POST /api/cart/add` - Add item to cart
- `PUT /api/cart/update` - Update cart item
- `DELETE /api/cart/remove` - Remove from cart
- `GET /api/wishlist` - Get user's wishlist
- `POST /api/wishlist/add` - Add to wishlist
- `DELETE /api/wishlist/remove` - Remove from wishlist

### Admin (Requires Authentication)
- `GET /api/admin/dashboard` - Dashboard analytics
- `GET /api/admin/users` - Manage users
- `GET /api/admin/orders` - Manage orders
- `GET /api/admin/products` - Manage products

## Configuration

### Environment Variables (.env)
```env
# Server
PORT=3001
NODE_ENV=development

# Database
DB_HOST=localhost
DB_PORT=3306
DB_NAME=luxe_fashion
DB_USER=root
DB_PASSWORD=your_password

# JWT
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=7d

# CORS
FRONTEND_URL=http://localhost:3001
```

## File Structure

```
luxe-fashion-app/
├── server.js              # Main server file (consolidated)
├── package.json           # Dependencies and scripts
├── .env                   # Environment configuration
├── api-config.js          # Frontend API configuration
├── index.html             # Homepage
├── products.html          # Products page
├── admin.html             # Admin dashboard
├── checkout.html          # Checkout page
├── script.js              # Main frontend JavaScript
├── products.js            # Products page JavaScript
├── admin.js               # Admin dashboard JavaScript
├── styles.css             # Main stylesheet
├── config/                # Server configuration
├── controllers/           # API controllers
├── middleware/            # Express middleware
├── routes/                # API routes
├── database/              # Database scripts
└── utils/                 # Utility functions
```

## Key Changes Made

### 1. Server Consolidation
- Removed separate `frontend-server.js`
- Added static file serving to main `server.js`
- Configured SPA routing for frontend navigation

### 2. API Configuration
- Updated `api-config.js` to use relative URLs
- Removed hardcoded localhost URLs
- CORS configured for same-origin requests

### 3. Routing
- API routes: `/api/*` return JSON responses
- Frontend routes: All other routes serve `index.html` for SPA
- Static assets: Served directly (CSS, JS, images)

### 4. Cleanup
- Removed 25+ test and debug files
- Removed development scripts
- Streamlined package.json

## Testing

Run the comprehensive test suite:
```bash
node test-consolidated-app.js
```

This tests:
- API endpoints functionality
- Frontend page serving
- Static asset delivery
- SPA routing
- Error handling

## Deployment

### Production Deployment
1. Set `NODE_ENV=production` in environment
2. Configure production database settings
3. Set secure JWT secret
4. Configure CORS for production domain
5. Use process manager (PM2) for production:

```bash
npm install -g pm2
pm2 start server.js --name "luxe-fashion"
```

### Docker Deployment
The application is ready for containerization with a simple Dockerfile.

## Security Features

- Helmet.js for security headers
- Rate limiting on API routes
- Input sanitization and validation
- JWT-based authentication
- CORS protection
- SQL injection prevention

## Support

For issues or questions:
1. Check the logs for error details
2. Verify database connection
3. Ensure all environment variables are set
4. Run the test suite to identify issues

The consolidated application maintains all original functionality while providing a simpler, more maintainable architecture.
