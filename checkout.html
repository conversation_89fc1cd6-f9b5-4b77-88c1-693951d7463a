<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout - Luxe Fashion</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#4B0082',
                        'secondary': '#D8BFD8',
                        'accent': '#9370DB',
                        'purple-light': '#E6E6FA',
                        'purple-dark': '#301934'
                    },
                    fontFamily: {
                        'display': ['Inter', 'system-ui', 'sans-serif'],
                        'body': ['Inter', 'system-ui', 'sans-serif']
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'fade-in-up': 'fadeInUp 0.6s ease-out',
                        'pulse-slow': 'pulse 3s infinite',
                        'bounce-slow': 'bounce 2s infinite'
                    },
                    backdropBlur: {
                        'xs': '2px',
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body class="bg-white text-gray-800 font-body">
    <!-- Navigation -->
    <nav class="bg-white/90 backdrop-blur-xl shadow-2xl fixed top-4 left-4 right-4 z-50 transition-all duration-300 rounded-2xl border border-white/20" id="navbar">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="index.html" class="text-2xl font-bold text-primary font-display tracking-tight">Luxe Fashion</a>
                </div>

                <!-- Desktop Menu -->
                <div class="hidden lg:block">
                    <div class="ml-10 flex items-baseline space-x-8">
                        <a href="index.html" class="text-gray-700 hover:text-primary transition duration-300 font-medium relative group">
                            Home
                            <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                        </a>
                        <div class="relative group">
                            <a href="#" class="text-gray-700 hover:text-primary transition duration-300 font-medium flex items-center">
                                Women <i class="fas fa-chevron-down ml-1 text-xs"></i>
                            </a>
                        </div>
                        <div class="relative group">
                            <a href="#" class="text-gray-700 hover:text-primary transition duration-300 font-medium flex items-center">
                                Men <i class="fas fa-chevron-down ml-1 text-xs"></i>
                            </a>
                        </div>
                        <a href="#" class="text-red-500 hover:text-red-600 transition duration-300 font-medium relative group">
                            Sale
                            <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-red-500 transition-all duration-300 group-hover:w-full"></span>
                        </a>
                    </div>
                </div>

                <!-- Icons -->
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <button class="text-gray-700 hover:text-primary cursor-pointer transition duration-300 relative" id="wishlist-btn">
                            <i class="fas fa-heart"></i>
                            <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" id="wishlist-count">0</span>
                        </button>
                        <!-- Wishlist Dropdown -->
                        <div id="wishlist-dropdown" class="absolute top-full right-0 mt-2 w-80 bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl opacity-0 invisible transition-all duration-300 z-50 border border-white/20">
                            <div class="p-4">
                                <h3 class="font-semibold text-gray-800 mb-3">Wishlist</h3>
                                <div id="wishlist-items" class="space-y-3 mb-4">
                                    <div class="text-center text-gray-500 py-8">Your wishlist is empty</div>
                                </div>
                                <div class="border-t pt-3">
                                    <div class="flex justify-between items-center mb-3">
                                        <span class="font-semibold">Items:</span>
                                        <span class="font-bold text-primary" id="wishlist-item-count">0</span>
                                    </div>
                                    <button class="w-full bg-red-500 text-white py-2 rounded-lg hover:bg-red-600 transition duration-300">
                                        View All Wishlist
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="relative">
                        <button class="text-gray-700 hover:text-primary cursor-pointer transition duration-300 relative" id="cart-btn">
                            <i class="fas fa-shopping-bag"></i>
                            <span class="absolute -top-2 -right-2 bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" id="cart-count">0</span>
                        </button>
                        <!-- Cart Dropdown -->
                        <div id="cart-dropdown" class="absolute top-full right-0 mt-2 w-80 bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl opacity-0 invisible transition-all duration-300 z-50 border border-white/20">
                            <div class="p-4">
                                <h3 class="font-semibold text-gray-800 mb-3">Shopping Cart</h3>
                                <div id="cart-items" class="space-y-3 mb-4">
                                    <div class="text-center text-gray-500 py-8">Your cart is empty</div>
                                </div>
                                <div class="border-t pt-3">
                                    <div class="flex justify-between items-center mb-3">
                                        <span class="font-semibold">Total:</span>
                                        <span class="font-bold text-primary" id="cart-total">$0.00</span>
                                    </div>
                                    <a href="checkout.html" class="block w-full bg-primary text-white py-2 rounded-lg hover:bg-accent transition duration-300 text-center">
                                        Checkout
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Auth Buttons -->
                    <div class="auth-buttons hidden lg:flex items-center space-x-3">
                        <button onclick="openLoginModal()" class="text-gray-700 hover:text-primary font-medium transition duration-300">
                            Login
                        </button>
                        <button onclick="openSignupModal()" class="bg-primary text-white px-4 py-2 rounded-full font-medium hover:bg-accent transition duration-300">
                            Sign Up
                        </button>
                    </div>

                    <!-- Mobile menu button -->
                    <button id="mobile-menu-btn" class="lg:hidden text-gray-700 hover:text-primary">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <section class="pt-24 pb-8 bg-gradient-to-b from-gray-50 to-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <nav class="flex items-center space-x-2 text-sm text-gray-600 mb-8">
                <a href="index.html" class="hover:text-primary transition duration-300">Home</a>
                <i class="fas fa-chevron-right text-xs"></i>
                <span class="text-primary font-medium">Checkout</span>
            </nav>

            <!-- Page Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-4 font-display">Checkout</h1>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">Complete your order and enjoy fast, secure delivery</p>
            </div>
        </div>
    </section>

    <!-- Checkout Content -->
    <section class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Customer Information Form -->
                <div class="space-y-8">
                    <div class="bg-white rounded-2xl shadow-lg p-8 border border-gray-100">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6 font-display">Customer Information</h2>

                        <form id="checkout-form" class="space-y-6">
                            <!-- Contact Information -->
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">Contact Information</h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label for="first-name" class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                                        <input type="text" id="first-name" name="first-name" required
                                               class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                                    </div>
                                    <div>
                                        <label for="last-name" class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                                        <input type="text" id="last-name" name="last-name" required
                                               class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                                    </div>
                                </div>
                                <div class="mt-4">
                                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                                    <input type="email" id="email" name="email" required
                                           class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                                </div>
                                <div class="mt-4">
                                    <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                                    <input type="tel" id="phone" name="phone" required
                                           class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                                </div>
                            </div>

                            <!-- Shipping Address -->
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">Shipping Address</h3>
                                <div class="space-y-4">
                                    <div>
                                        <label for="address" class="block text-sm font-medium text-gray-700 mb-2">Street Address</label>
                                        <input type="text" id="address" name="address" required
                                               class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                                    </div>
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div>
                                            <label for="city" class="block text-sm font-medium text-gray-700 mb-2">City</label>
                                            <input type="text" id="city" name="city" required
                                                   class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                                        </div>
                                        <div>
                                            <label for="state" class="block text-sm font-medium text-gray-700 mb-2">State</label>
                                            <input type="text" id="state" name="state" required
                                                   class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                                        </div>
                                        <div>
                                            <label for="zip" class="block text-sm font-medium text-gray-700 mb-2">ZIP Code</label>
                                            <input type="text" id="zip" name="zip" required
                                                   class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Billing Address -->
                            <div>
                                <div class="flex items-center mb-4">
                                    <input type="checkbox" id="same-as-shipping" name="same-as-shipping" checked
                                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                    <label for="same-as-shipping" class="ml-2 block text-sm text-gray-700">
                                        Billing address is the same as shipping address
                                    </label>
                                </div>

                                <div id="billing-address" class="hidden space-y-4">
                                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Billing Address</h3>
                                    <!-- Billing address fields would go here -->
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="space-y-8">
                    <div class="bg-white rounded-2xl shadow-lg p-8 border border-gray-100">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6 font-display">Order Summary</h2>

                        <!-- Order Items -->
                        <div id="checkout-items" class="space-y-4 mb-6">
                            <div class="text-center text-gray-500 py-8">Your cart is empty</div>
                        </div>

                        <!-- Order Totals -->
                        <div class="border-t pt-6 space-y-3">
                            <div class="flex justify-between text-gray-600">
                                <span>Subtotal:</span>
                                <span id="checkout-subtotal">$0.00</span>
                            </div>
                            <div class="flex justify-between text-gray-600">
                                <span>Shipping:</span>
                                <span class="text-green-600 font-medium">Free</span>
                            </div>
                            <div class="flex justify-between text-gray-600">
                                <span>Tax:</span>
                                <span id="checkout-tax">$0.00</span>
                            </div>
                            <div class="border-t pt-3 flex justify-between text-xl font-bold text-gray-900">
                                <span>Total:</span>
                                <span id="checkout-total">$0.00</span>
                            </div>
                        </div>

                        <!-- Complete Order Button -->
                        <button type="submit" form="checkout-form"
                                class="w-full mt-8 bg-primary text-white py-4 rounded-lg text-lg font-semibold hover:bg-accent transition-all duration-300 transform hover:scale-105 hover:shadow-xl">
                            <i class="fas fa-lock mr-2"></i>
                            Complete Order
                        </button>

                        <!-- Security Notice -->
                        <div class="mt-6 text-center text-sm text-gray-500">
                            <i class="fas fa-shield-alt text-green-500 mr-1"></i>
                            Your payment information is secure and encrypted
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Login Modal -->
    <div id="login-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div id="login-modal-content" class="bg-white rounded-2xl shadow-2xl max-w-md w-full p-8 transform scale-95 transition-transform duration-300">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 font-display">Welcome Back</h2>
                    <p class="text-gray-600 mt-2">Sign in to your account</p>
                </div>

                <form id="login-form" class="space-y-6">
                    <div class="relative">
                        <input type="email" id="login-email" class="peer w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 placeholder-transparent" placeholder="Email" required>
                        <label for="login-email" class="absolute left-4 top-3 text-gray-500 transition-all duration-300 pointer-events-none">Email Address</label>
                    </div>

                    <div class="relative">
                        <input type="password" id="login-password" class="peer w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 placeholder-transparent" placeholder="Password" required>
                        <label for="login-password" class="absolute left-4 top-3 text-gray-500 transition-all duration-300 pointer-events-none">Password</label>
                        <button type="button" onclick="togglePasswordVisibility('login-password')" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                            <i id="login-password-icon" class="fas fa-eye"></i>
                        </button>
                    </div>

                    <div class="flex items-center justify-between">
                        <label class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-600">Remember me</span>
                        </label>
                        <a href="#" class="text-sm text-primary hover:text-accent">Forgot password?</a>
                    </div>

                    <button type="submit" class="w-full bg-primary text-white py-3 rounded-lg font-semibold hover:bg-accent transition-all duration-300 transform hover:scale-105">
                        Sign In
                    </button>
                </form>

                <div class="mt-6 text-center">
                    <p class="text-gray-600">Don't have an account?
                        <button onclick="switchToSignup()" class="text-primary hover:text-accent font-medium">Sign up</button>
                    </p>
                </div>

                <button onclick="closeAuthModal()" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Signup Modal -->
    <div id="signup-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div id="signup-modal-content" class="bg-white rounded-2xl shadow-2xl max-w-md w-full p-8 transform scale-95 transition-transform duration-300">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 font-display">Join Luxe Fashion</h2>
                    <p class="text-gray-600 mt-2">Create your account today</p>
                </div>

                <form id="signup-form" class="space-y-6">
                    <div class="relative">
                        <input type="text" id="signup-name" class="peer w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 placeholder-transparent" placeholder="Full Name" required>
                        <label for="signup-name" class="absolute left-4 top-3 text-gray-500 transition-all duration-300 pointer-events-none">Full Name</label>
                    </div>

                    <div class="relative">
                        <input type="email" id="signup-email" class="peer w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 placeholder-transparent" placeholder="Email" required>
                        <label for="signup-email" class="absolute left-4 top-3 text-gray-500 transition-all duration-300 pointer-events-none">Email Address</label>
                        <div id="email-validation" class="hidden text-red-500 text-xs mt-1">Please enter a valid email address</div>
                    </div>

                    <div class="relative">
                        <input type="password" id="signup-password" class="peer w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 placeholder-transparent" placeholder="Password" required>
                        <label for="signup-password" class="absolute left-4 top-3 text-gray-500 transition-all duration-300 pointer-events-none">Password</label>
                        <button type="button" onclick="togglePasswordVisibility('signup-password')" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                            <i id="signup-password-icon" class="fas fa-eye"></i>
                        </button>
                    </div>

                    <div id="password-strength" class="hidden">
                        <div class="flex space-x-1 mb-2">
                            <div id="strength-1" class="h-1 flex-1 bg-gray-200 rounded"></div>
                            <div id="strength-2" class="h-1 flex-1 bg-gray-200 rounded"></div>
                            <div id="strength-3" class="h-1 flex-1 bg-gray-200 rounded"></div>
                            <div id="strength-4" class="h-1 flex-1 bg-gray-200 rounded"></div>
                        </div>
                        <p id="strength-text" class="text-xs text-gray-500">Password strength</p>
                    </div>

                    <div class="relative">
                        <input type="password" id="signup-confirm-password" class="peer w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 placeholder-transparent" placeholder="Confirm Password" required>
                        <label for="signup-confirm-password" class="absolute left-4 top-3 text-gray-500 transition-all duration-300 pointer-events-none">Confirm Password</label>
                        <div id="password-match" class="hidden text-red-500 text-xs mt-1">Passwords do not match</div>
                    </div>

                    <label class="flex items-start">
                        <input type="checkbox" id="terms-checkbox" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded mt-1" required>
                        <span class="ml-2 text-sm text-gray-600">I agree to the <a href="#" class="text-primary hover:text-accent">Terms of Service</a> and <a href="#" class="text-primary hover:text-accent">Privacy Policy</a></span>
                    </label>

                    <button type="submit" class="w-full bg-primary text-white py-3 rounded-lg font-semibold hover:bg-accent transition-all duration-300 transform hover:scale-105">
                        Create Account
                    </button>
                </form>

                <div class="mt-6 text-center">
                    <p class="text-gray-600">Already have an account?
                        <button onclick="switchToLogin()" class="text-primary hover:text-accent font-medium">Sign in</button>
                    </p>
                </div>

                <button onclick="closeAuthModal()" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="script.js"></script>
    <script>
        // Checkout specific functionality
        document.addEventListener('DOMContentLoaded', function() {
            loadCheckoutItems();
            setupCheckoutForm();
        });

        function loadCheckoutItems() {
            const checkoutItems = document.getElementById('checkout-items');
            const subtotalElement = document.getElementById('checkout-subtotal');
            const taxElement = document.getElementById('checkout-tax');
            const totalElement = document.getElementById('checkout-total');

            if (cart.length === 0) {
                checkoutItems.innerHTML = '<div class="text-center text-gray-500 py-8">Your cart is empty</div>';
                return;
            }

            const subtotal = cart.reduce((sum, item) => sum + item.price, 0);
            const tax = subtotal * 0.08; // 8% tax
            const total = subtotal + tax;

            checkoutItems.innerHTML = cart.map(item => `
                <div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                    <div class="w-16 h-16 bg-secondary rounded-lg"></div>
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-800">${item.name}</h4>
                        <p class="text-sm text-gray-600">Quantity: 1</p>
                    </div>
                    <div class="text-right">
                        <p class="font-bold text-primary">$${item.price.toFixed(2)}</p>
                    </div>
                </div>
            `).join('');

            subtotalElement.textContent = `$${subtotal.toFixed(2)}`;
            taxElement.textContent = `$${tax.toFixed(2)}`;
            totalElement.textContent = `$${total.toFixed(2)}`;
        }

        function setupCheckoutForm() {
            const form = document.getElementById('checkout-form');
            const sameAsShippingCheckbox = document.getElementById('same-as-shipping');
            const billingAddressDiv = document.getElementById('billing-address');

            sameAsShippingCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    billingAddressDiv.classList.add('hidden');
                } else {
                    billingAddressDiv.classList.remove('hidden');
                }
            });

            form.addEventListener('submit', function(e) {
                e.preventDefault();

                if (cart.length === 0) {
                    showNotification('Your cart is empty', 'error');
                    return;
                }

                // Simulate order processing
                showNotification('Processing your order...', 'info');

                setTimeout(() => {
                    showNotification('Order placed successfully!', 'success');
                    cart = [];
                    updateCartUI();
                    loadCheckoutItems();
                    form.reset();
                }, 2000);
            });
        }
    </script>
</body>
</html>
